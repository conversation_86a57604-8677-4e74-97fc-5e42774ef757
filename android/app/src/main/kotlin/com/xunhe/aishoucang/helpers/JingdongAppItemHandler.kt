package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import com.xunhe.aishoucang.helpers.ContentTypeConstants
import com.xunhe.aishoucang.helpers.SharePanelHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor
import com.xunhe.aishoucang.api.BookMark
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 京东应用收藏项处理器
 * 参考小红书的逻辑，实现各个场景的分发处理
 */
object JingdongAppItemHandler {
    private const val TAG = "JingdongAppItemHandler"

    // 时间格式化工具
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    /**
     * 处理京东应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.i(TAG, "京东处理器开始处理")
        val contentType = SharePanelHelper.getCurrentContentType()

        // 参考小红书的逻辑，根据内容类型分发到不同的处理函数
        when (contentType) {
            ContentTypeConstants.JINGDONG_TYPE_PRODUCT -> {
                handleProduct(context, appPackage, clipboardContent, favoriteItem)
            }
            ContentTypeConstants.JINGDONG_TYPE_SHOP -> {
                handleShop(context, appPackage, clipboardContent, favoriteItem)
            }
            ContentTypeConstants.JINGDONG_TYPE_LIVE -> {
                handleLive(context, appPackage, clipboardContent, favoriteItem)
            }
            ContentTypeConstants.JINGDONG_TYPE_ACTIVITY -> {
                handleActivity(context, appPackage, clipboardContent, favoriteItem)
            }
            else -> {
                CustomToastHelper.showToast(context, "已保存京东内容到「${favoriteItem?.name}」")
            }
        }
    }

    /**
     * 处理京东商品
     */
    private fun handleProduct(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.i(TAG, "开始处理京东商品")
        Log.i(TAG, "京东剪切板内容: $clipboardContent")

        val extractedLink = extractJingdongLink(clipboardContent)

        if (extractedLink != null) {
            Log.d(TAG, "开始处理京东商品链接: $extractedLink")

            // 显示加载动画
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

            // 使用WebViewHtmlExtractor执行业务特定的JavaScript
            WebViewHtmlExtractor.executeBusinessJavaScript(
                context,
                extractedLink,
                "JingdongGoods"
            ) { result, error ->
                if (error != null) {
                    // 隐藏加载动画
                    floatingWindowHelper.hideLoading()

                    // 显示错误消息
                    CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                    Log.e(TAG, "提取商品信息失败: $error")
                    return@executeBusinessJavaScript
                }

                if (result != null) {
                    try {
                        // 解析JSON结果
                        val jsonObject = org.json.JSONObject(result)

                        // 检查是否有错误
                        if (jsonObject.has("error")) {
                            val errorMessage = jsonObject.getString("error")
                            Log.e(TAG, "提取商品数据时JavaScript报错: $errorMessage")

                            // 隐藏加载动画
                            floatingWindowHelper.hideLoading()

                            // 显示错误消息
                            CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                            return@executeBusinessJavaScript
                        }

                        // 提取数据
                        val title = if (jsonObject.has("title")) jsonObject.getString("title") else null
                        val price = if (jsonObject.has("price")) jsonObject.getString("price") else null
                        val coverImage = if (jsonObject.has("coverImage")) jsonObject.getString("coverImage") else null

                        // 提取schemeURL
                        val schemeURL = if (jsonObject.has("schemeURL")) jsonObject.getString("schemeURL") else null

                        // 提取店铺信息
                        var shopName: String? = null
                        var shopAvatar: String? = null
                        if (jsonObject.has("shop") && !jsonObject.isNull("shop")) {
                            val shopObject = jsonObject.getJSONObject("shop")
                            if (shopObject.has("name")) {
                                shopName = shopObject.getString("name")
                            }
                            if (shopObject.has("avatar")) {
                                shopAvatar = shopObject.getString("avatar")
                            }
                        }

                        Log.d(TAG, "提取结果汇总:")
                        Log.d(TAG, "- 标题: ${title ?: "未知"}")
                        Log.d(TAG, "- 价格: ${price ?: "未知"}")
                        Log.d(TAG, "- 封面图: ${coverImage ?: "未知"}")
                        Log.d(TAG, "- 店铺名称: ${shopName ?: "未知"}")
                        Log.d(TAG, "- 店铺头像: ${shopAvatar ?: "未知"}")
                        Log.d(TAG, "- SchemeURL: ${schemeURL ?: "未知"}")

                        // 获取平台类型
                        val platformType = SharePanelHelper.getCurrentPlatformType(schemeURL)
                        Log.d(TAG, "- 平台类型: $platformType")

                        // 直接调用BookMark.addBookMark，不进行OSS上传
                        BookMark.addBookMark(
                            context = context,
                            influencer_name = shopName,
                            influencer_avatar = shopAvatar, // 直接使用原始头像URL
                            cover = coverImage, // 直接使用原始封面URL
                            title = title,
                            desc = "", // 京东商品描述设置为空字符串
                            parent_id = favoriteItem?.id ?: "",
                            scheme_url = schemeURL ?: "",
                            platform_type = platformType,
                            callback = { success: Boolean, errorMessage: String? ->
                                // 隐藏悬浮窗上的加载动画
                                val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
                                floatingWindowHelper.hideLoading()

                                if (success) {
                                    CustomToastHelper.showShortToast(context, "收藏成功")
                                } else {
                                    CustomToastHelper.showShortToast(context, "保存失败: $errorMessage")
                                }
                            }
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "解析商品数据失败: ${e.message}", e)

                        // 隐藏加载动画
                        floatingWindowHelper.hideLoading()

                        // 显示错误消息
                        CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                    }
                } else {
                    // 隐藏加载动画
                    floatingWindowHelper.hideLoading()

                    // 显示错误消息
                    CustomToastHelper.showShortToast(context, "获取商品信息失败，请重试")
                    Log.e(TAG, "提取商品信息失败: 结果为空")
                }
            }
        } else {
            Log.e(TAG, "未能从剪贴板内容中提取到京东链接")
            CustomToastHelper.showShortToast(context, "提取链接失败，请重试")
        }
    }

    /**
     * 处理京东店铺
     */
    private fun handleShop(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.i(TAG, "开始处理京东店铺")
        CustomToastHelper.showToast(context, "已保存京东店铺到「${favoriteItem?.name}」")
    }

    /**
     * 处理京东直播
     */
    private fun handleLive(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.i(TAG, "开始处理京东直播")
        CustomToastHelper.showToast(context, "已保存京东直播到「${favoriteItem?.name}」")
    }

    /**
     * 处理京东活动
     */
    private fun handleActivity(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.i(TAG, "开始处理京东活动")
        CustomToastHelper.showToast(context, "已保存京东活动到「${favoriteItem?.name}」")
    }

    /**
     * 从剪贴板内容中提取链接
     * 直接提取https链接，支持京东分享格式
     *
     * 支持的格式示例：
     * - 【京东】https://3.cn/2hre-sLD 「京鲜生 黄肉红油桃净重3斤」
     *
     * @param content 剪贴板内容
     * @return 提取的链接，如果未找到则返回null
     */
    private fun extractJingdongLink(content: String?): String? {
        if (content.isNullOrEmpty()) {
            return null
        }

        try {
            Log.d(TAG, "开始提取链接，内容: $content")

            // 直接提取https链接
            val pattern = """https://[^\s]+""".toRegex()
            val matchResult = pattern.find(content)

            if (matchResult != null) {
                val link = matchResult.value
                Log.d(TAG, "提取到链接: $link")
                return link
            }

            Log.d(TAG, "未找到https链接")
            return null
        } catch (e: Exception) {
            Log.e(TAG, "提取京东链接时出错: ${e.message}", e)
            return null
        }
    }
}
