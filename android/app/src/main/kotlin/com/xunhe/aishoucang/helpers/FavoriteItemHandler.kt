package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 收藏项处理器
 * 处理收藏夹的点击事件，将链接保存到收藏夹
 */
object FavoriteItemHandler {
    private const val TAG = "FavoriteItemHandler"

    // 时间格式化工具
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

    // 定义各个应用的包名常量
    private const val PACKAGE_DOUYIN = "com.ss.android.ugc.aweme"
    private const val PACKAGE_KUAISHOU = "com.kuaishou.nebula"
    private const val PACKAGE_XIAOHONGSHU = "com.xingin.xhs"
    private const val PACKAGE_BILIBILI = "tv.danmaku.bili"
    private const val PACKAGE_WECHAT = "com.tencent.mm"
    private const val PACKAGE_MEITUAN = "com.sankuai.meituan"
    private const val PACKAGE_DOUBAN = "com.douban.frodo"
    private const val PACKAGE_PINDUODUO = "com.xunmeng.pinduoduo"
    private const val PACKAGE_TAOBAO = "com.taobao.taobao"
    private const val PACKAGE_JINGDONG = "com.jingdong.app.mall"

    /**
     * 处理收藏夹的点击事件
     * 根据应用包名分发到不同的处理器
     *
     * @param context 上下文
     * @param appPackage 当前应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 选择的收藏夹项
     */
    fun handleFavoriteItemClick(
        context: Context,
        appPackage: String?,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        // 记录开始时间
        val startTime = System.currentTimeMillis()
        val startTimeStr = timeFormat.format(Date(startTime))
        Log.i(TAG, "【时间日志】收藏开始: $startTimeStr")

        // 显示悬浮窗上的加载动画
        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
        
        Log.i(TAG, "【时间日志】显示加载动画: ${getElapsedTimeLog(startTime)}")

        // 根据应用包名分发到对应的处理器
        when {
            appPackage == null -> {
                Log.e(TAG, "应用包名为空，无法分发")
                Toast.makeText(context, "无法识别当前应用", Toast.LENGTH_SHORT).show()
                // 隐藏加载动画
                floatingWindowHelper.hideLoading()
            }
            appPackage.startsWith(PACKAGE_DOUYIN) -> {
                Log.d(TAG, "分发到抖音处理器")
                DouyinAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_KUAISHOU) -> {
                Log.d(TAG, "分发到快手处理器")
                KuaishouAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_XIAOHONGSHU) -> {
                Log.d(TAG, "分发到小红书处理器")
                Log.i(TAG, "【时间日志】开始处理小红书收藏: ${getElapsedTimeLog(startTime)}")
                // 将开始时间传递给小红书处理器
                XiaohongshuAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            appPackage.startsWith(PACKAGE_BILIBILI) -> {
                Log.d(TAG, "分发到B站处理器")
                BilibiliAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_WECHAT) -> {
                Log.d(TAG, "分发到微信处理器")
                WechatAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_MEITUAN) -> {
                Log.d(TAG, "分发到美团处理器")
                MeituanAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_DOUBAN) -> {
                Log.d(TAG, "分发到豆瓣处理器")
                // 使用专门的豆瓣处理器
                DoubanAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem, startTime)
            }
            appPackage.startsWith(PACKAGE_PINDUODUO) -> {
                Log.d(TAG, "分发到拼多多处理器")
                PinDuoDuoAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_TAOBAO) -> {
                Log.d(TAG, "分发到淘宝处理器")
                TaobaoAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            appPackage.startsWith(PACKAGE_JINGDONG) -> {
                Log.d(TAG, "分发到京东处理器")
                JingdongAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
            else -> {
                Log.d(TAG, "未知应用，使用通用处理器")
                GenericAppItemHandler.handle(context, appPackage, clipboardContent, favoriteItem)
            }
        }
    }

    /**
     * 获取从开始时间到现在的耗时日志
     *
     * @param startTime 开始时间（毫秒）
     * @return 格式化的耗时日志
     */
    fun getElapsedTimeLog(startTime: Long): String {
        val currentTime = System.currentTimeMillis()
        val elapsedTime = currentTime - startTime
        val currentTimeStr = timeFormat.format(Date(currentTime))
        return "当前时间: $currentTimeStr, 耗时: ${elapsedTime}ms"
    }
}