package com.xunhe.aishoucang.helpers

import android.content.Context
import android.util.Log
import com.xunhe.aishoucang.api.BookMark
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.views.share_panel.SharePanelItem
import org.json.JSONObject

/**
 * 淘宝应用收藏项处理器
 */
object TaobaoAppItemHandler {
    private const val TAG = "TaobaoAppItemHandler"

    /**
     * 淘宝商品信息数据类
     */
    data class TaobaoProductInfo(
        val title: String?,
        val link: String?
    )

    /**
     * 处理淘宝应用的收藏项
     *
     * @param context 上下文
     * @param appPackage 应用包名
     * @param clipboardContent 剪贴板内容
     * @param favoriteItem 收藏夹项
     */
    fun handle(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "处理淘宝应用收藏项:")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        // 获取当前内容类型
        val contentType = SharePanelHelper.getCurrentContentType()
        Log.d(TAG, "- 内容类型: $contentType")

        // 只支持商品类型
        if (contentType == ContentTypeConstants.TAOBAO_TYPE_PRODUCT) {
            handleProduct(context, appPackage, clipboardContent, favoriteItem)
        } else {
            // 默认按商品处理
            Log.d(TAG, "未知内容类型，按商品处理")
            handleProduct(context, appPackage, clipboardContent, favoriteItem)
        }
    }

    /**
     * 处理淘宝商品
     */
    private fun handleProduct(
        context: Context,
        appPackage: String,
        clipboardContent: String?,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "开始处理淘宝商品")
        Log.d(TAG, "- 应用包名: $appPackage")
        Log.d(TAG, "- 剪贴板内容: $clipboardContent")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        if (clipboardContent.isNullOrBlank()) {
            Log.e(TAG, "剪贴板内容为空，无法处理")
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "剪贴板内容为空，请重试")
            return
        }

        // 显示加载动画
        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        Log.d(TAG, "淘宝商品剪切板内容: $clipboardContent")

        // 从剪贴板内容中提取商品信息
        val productInfo = extractTaobaoProductInfo(clipboardContent)
        Log.d(TAG, "提取的商品信息: $productInfo")

        // 检查是否成功提取到链接
        if (productInfo.link.isNullOrBlank()) {
            Log.e(TAG, "未能从剪贴板内容中提取到有效的淘宝链接")
            val floatingWindowHelper = FloatingWindowHelper.getInstance(context)
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "未能识别淘宝商品链接，请重试")
            return
        }

        // 使用WebView加载提取到的淘宝链接并执行JS文件
        Log.d(TAG, "开始使用WebView加载淘宝链接: ${productInfo.link}")

        WebViewHtmlExtractor.executeBusinessJavaScript(
            context,
            productInfo.link!!, // 使用提取到的淘宝链接
            "TaobaoGoods", // 对应assets/js/TaobaoGoods.js文件
            emptyMap() // 不需要替换参数
        ) { result, error ->
            handleJavaScriptResult(context, result, error, productInfo, favoriteItem)
        }
    }



    /**
     * 从剪贴板内容中提取淘宝商品信息
     *
     * 复制内容格式如下：
     * 【淘宝】假一赔四 https://e.tb.cn/h.6EmaDTYBY9HKNPO?tk=o6bNVOOFfKR MF937 「KEIKO 落日橘条纹短袖t恤女夏季小众不撞款穿搭色织提花圆领上衣」
     *
     * @param clipboardContent 剪贴板内容
     * @return TaobaoProductInfo 包含商品标题和链接
     */
    private fun extractTaobaoProductInfo(clipboardContent: String): TaobaoProductInfo {
        Log.d(TAG, "开始提取淘宝商品信息")

        var title: String? = null
        var link: String? = null

        try {
            // 正则匹配淘宝链接 (https://e.tb.cn/xxx 或 https://m.tb.cn/xxx)
            val linkPattern = Regex("https://[em]\\.tb\\.cn/[^\\s]+")
            val linkMatch = linkPattern.find(clipboardContent)
            if (linkMatch != null) {
                link = linkMatch.value
                Log.d(TAG, "提取到链接: $link")
            }

            // 正则匹配商品标题（在「」中的内容）
            val titlePattern = Regex("「([^」]+)」")
            val titleMatch = titlePattern.find(clipboardContent)
            if (titleMatch != null) {
                title = titleMatch.groupValues[1]
                Log.d(TAG, "提取到标题: $title")
            }

        } catch (e: Exception) {
            Log.e(TAG, "提取淘宝商品信息失败: ${e.message}", e)
        }

        return TaobaoProductInfo(title, link)
    }

    /**
     * 从页面URL中提取商品ID并构建schemeURL
     *
     * 页面URL格式示例：
     * "https://main.m.taobao.com/security-h5-detail/home?id=912283845073&price=158&sourceType=item..."
     *
     * 构建的schemeURL格式：
     * "taobao://item.taobao.com/item.html?id=商品ID"
     *
     * @param pageUrl 页面URL
     * @return 构建的schemeURL，如果提取失败则返回null
     */
    private fun buildSchemeUrlFromPageUrl(pageUrl: String): String? {
        Log.d(TAG, "开始从页面URL构建schemeURL: $pageUrl")

        try {
            // 使用正则表达式提取id参数
            val idPattern = Regex("[?&]id=([^&]+)")
            val idMatch = idPattern.find(pageUrl)

            if (idMatch != null) {
                val productId = idMatch.groupValues[1]
                Log.d(TAG, "提取到商品ID: $productId")

                // 构建schemeURL
                val schemeUrl = "taobao://item.taobao.com/item.html?id=$productId"
                Log.d(TAG, "构建的schemeURL: $schemeUrl")

                return schemeUrl
            } else {
                Log.e(TAG, "未能从页面URL中提取到商品ID")
                return null
            }
        } catch (e: Exception) {
            Log.e(TAG, "从页面URL构建schemeURL失败: ${e.message}", e)
            return null
        }
    }

    /**
     * 处理JavaScript执行结果
     *
     * @param context 上下文
     * @param result JavaScript执行结果
     * @param error 错误信息
     * @param productInfo 商品信息
     * @param favoriteItem 收藏夹项
     */
    private fun handleJavaScriptResult(
        context: Context,
        result: String?,
        error: String?,
        productInfo: TaobaoProductInfo,
        favoriteItem: SharePanelItem?
    ) {
        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        if (error != null) {
            Log.e(TAG, "TaobaoGoods.js执行失败: $error")
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "获取商品信息失败，请重试")
            return
        }

        if (result != null) {
            Log.d(TAG, "TaobaoGoods.js执行成功，结果: $result")

            try {
                // 解析JavaScript返回的JSON结果
                val jsonResult = JSONObject(result)

                // 从JavaScript结果中提取商品信息
                val jsShopName = if (jsonResult.has("shopName")) jsonResult.getString("shopName") else ""
                val jsShopAvatar = if (jsonResult.has("shopAvatar")) jsonResult.getString("shopAvatar") else ""
                val jsCoverImage = if (jsonResult.has("coverImage")) jsonResult.getString("coverImage") else ""
                val jsUrl = if (jsonResult.has("url")) jsonResult.getString("url") else ""

                Log.d(TAG, "从JS结果中提取的信息:")
                Log.d(TAG, "- 店铺名称: $jsShopName")
                Log.d(TAG, "- 店铺头像: $jsShopAvatar")
                Log.d(TAG, "- 封面图片: $jsCoverImage")
                Log.d(TAG, "- 页面URL: $jsUrl")

                // 从URL中提取商品ID并构建schemeURL
                val schemeUrl = buildSchemeUrlFromPageUrl(jsUrl)

                if (schemeUrl.isNullOrBlank()) {
                    Log.e(TAG, "未能从页面URL中提取到商品ID构建scheme URL")
                    floatingWindowHelper.hideLoading()
                    CustomToastHelper.showToast(context, "无法获取淘宝商品链接，请重试")
                    return
                }

                // 保存书签，使用JavaScript提取的信息
                saveBookmark(
                    context = context,
                    productTitle = productInfo.title,
                    shopName = jsShopName,
                    shopAvatar = jsShopAvatar,
                    coverImage = jsCoverImage,
                    schemeUrl = schemeUrl,
                    favoriteItem = favoriteItem
                )

            } catch (e: Exception) {
                Log.e(TAG, "解析TaobaoGoods.js结果失败: ${e.message}", e)
                floatingWindowHelper.hideLoading()
                CustomToastHelper.showToast(context, "处理淘宝内容时出错，请重试")
            }
        } else {
            Log.e(TAG, "TaobaoGoods.js执行结果为空")
            floatingWindowHelper.hideLoading()
            CustomToastHelper.showToast(context, "处理淘宝内容时出错，请重试")
        }
    }

    /**
     * 保存书签
     *
     * @param context 上下文
     * @param productTitle 商品标题
     * @param shopName 店铺名称
     * @param shopAvatar 店铺头像
     * @param coverImage 封面图片
     * @param schemeUrl scheme URL
     * @param favoriteItem 收藏夹项
     */
    private fun saveBookmark(
        context: Context,
        productTitle: String?,
        shopName: String,
        shopAvatar: String,
        coverImage: String,
        schemeUrl: String,
        favoriteItem: SharePanelItem?
    ) {
        Log.d(TAG, "开始保存淘宝书签")
        Log.d(TAG, "- 商品标题: $productTitle")
        Log.d(TAG, "- 店铺名称: $shopName")
        Log.d(TAG, "- 店铺头像: $shopAvatar")
        Log.d(TAG, "- 封面图片: $coverImage")
        Log.d(TAG, "- scheme URL: $schemeUrl")
        Log.d(TAG, "- 收藏夹ID: ${favoriteItem?.id}")
        Log.d(TAG, "- 收藏夹名称: ${favoriteItem?.name}")

        val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

        // 获取平台类型
        val platformType = SharePanelHelper.getCurrentPlatformType(schemeUrl)

        // 调用BookMark API保存书签，使用JavaScript提取的信息
        BookMark.addBookMark(
            context = context,
            influencer_name = shopName.ifBlank { "淘宝商品" }, // 使用店铺名称，如果为空则使用"淘宝商品"
            influencer_avatar = shopAvatar, // 使用店铺头像
            cover = coverImage, // 使用封面图片
            title = productTitle ?: "", // 使用商品标题，如果为空则设为空字符串
            desc = "", // 描述设为空字符串
            parent_id = favoriteItem?.id ?: "",
            scheme_url = schemeUrl,
            platform_type = platformType
        ) { success, errorMessage ->
            // 隐藏加载动画
            floatingWindowHelper.hideLoading()

            if (success) {
                CustomToastHelper.showToast(context, "主人，收藏成功啦~")
                Log.d(TAG, "淘宝书签保存成功")
            } else {
                CustomToastHelper.showToast(context, "哎呀，收藏失败~请稍后再试")
                Log.e(TAG, "淘宝书签保存失败: $errorMessage")
            }
        }
    }
}
